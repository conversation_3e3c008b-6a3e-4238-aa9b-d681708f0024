#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASC文件处理配置文件
请根据您的需求修改以下参数
"""

# ===========================================
# 基本配置
# ===========================================

# 输入文件夹列表 - 包含要处理的ASC文件的文件夹
INPUT_FOLDERS = [
    'pet_2023',
    'pet_2024'
]

# 输出基础文件夹 - 处理后的文件将保存在这里
OUTPUT_BASE_FOLDER = 'processed_asc'

# ===========================================
# 裁剪配置
# ===========================================

# 是否进行裁剪
ENABLE_CLIP = True

# 裁剪范围 (xmin, ymin, xmax, ymax) - 经纬度坐标
# 当前设置：中国大陆范围
CLIP_BOUNDS = (73.0, 18.0, 135.0, 54.0)

# ===========================================
# 重采样配置
# ===========================================

# 是否进行重采样
ENABLE_RESAMPLE = True

# 新的像元大小（度）
# 当前设置：0.01度 (约1公里)
NEW_CELLSIZE = 0.01

# ===========================================
# 高级配置
# ===========================================

# 无数据值
NODATA_VALUE = -9999

# 是否使用自定义处理配置（针对不同文件夹使用不同参数）
USE_CUSTOM_PROCESSING = False

# 自定义处理配置（仅在USE_CUSTOM_PROCESSING=True时生效）
CUSTOM_PROCESSING = {
    'pet_2023': {
        'clip_bounds': (73.0, 18.0, 135.0, 54.0),  # 全国范围
        'new_cellsize': 0.01,
        'enable_clip': True,
        'enable_resample': True
    },
    'pet_2024': {
        'clip_bounds': (110.0, 35.0, 120.0, 42.0),  # 华北地区
        'new_cellsize': 0.05,
        'enable_clip': True,
        'enable_resample': True
    }
}

# ===========================================
# 预定义区域（可以在CLIP_BOUNDS中引用）
# ===========================================

PREDEFINED_REGIONS = {
    'china_mainland': (73.0, 18.0, 135.0, 54.0),      # 中国大陆
    'north_china': (110.0, 35.0, 120.0, 42.0),        # 华北地区
    'yangtze_river': (90.0, 24.0, 122.0, 35.0),       # 长江流域
    'pearl_river': (102.0, 21.0, 117.0, 26.5),        # 珠江流域
    'xinjiang': (73.0, 34.0, 97.0, 49.0),             # 新疆
    'tibet': (78.0, 26.0, 99.0, 37.0),                # 西藏
    'northeast': (115.0, 39.0, 135.0, 54.0),          # 东北地区
    'southeast': (110.0, 18.0, 125.0, 32.0),          # 东南地区
}

# 使用预定义区域的示例（取消注释以使用）：
# CLIP_BOUNDS = PREDEFINED_REGIONS['china_mainland']
