# ASC文件批量裁剪和重采样工具使用说明

## 功能介绍

这个工具可以批量处理ASC格式的栅格文件，支持：
- **裁剪**：按指定地理范围裁剪数据
- **重采样**：改变数据的空间分辨率
- **批量处理**：一次处理多个文件夹中的所有ASC文件

## 文件说明

1. **`asc_clip_resample.py`** - 主处理脚本
2. **`config_template.py`** - 配置文件模板
3. **`使用说明_裁剪重采样.md`** - 本说明文档

## 快速开始

### 方法1：直接运行（使用默认配置）

```bash
python asc_clip_resample.py
```

默认配置：
- 处理 `pet_2023` 和 `pet_2024` 文件夹
- 裁剪到中国大陆范围 (73°-135°E, 18°-54°N)
- 重采样到 0.01度分辨率
- 输出到 `processed_asc` 文件夹

### 方法2：使用配置文件

1. **复制配置模板**：
   ```bash
   cp config_template.py config.py
   ```

2. **编辑配置文件** `config.py`：
   ```python
   # 修改输入文件夹
   INPUT_FOLDERS = ['pet_2023', 'pet_2024']
   
   # 修改裁剪范围（经度最小值, 纬度最小值, 经度最大值, 纬度最大值）
   CLIP_BOUNDS = (110.0, 35.0, 120.0, 42.0)  # 华北地区
   
   # 修改重采样分辨率
   NEW_CELLSIZE = 0.05  # 重采样到0.05度（约5公里）
   ```

3. **运行脚本**：
   ```bash
   python asc_clip_resample.py
   ```

## 配置参数详解

### 基本配置

```python
# 输入文件夹列表
INPUT_FOLDERS = ['pet_2023', 'pet_2024']

# 输出文件夹
OUTPUT_BASE_FOLDER = 'processed_asc'
```

### 裁剪配置

```python
# 是否启用裁剪
ENABLE_CLIP = True

# 裁剪范围 (西经, 南纬, 东经, 北纬)
CLIP_BOUNDS = (73.0, 18.0, 135.0, 54.0)
```

**常用区域范围**：
- 中国大陆：`(73.0, 18.0, 135.0, 54.0)`
- 华北地区：`(110.0, 35.0, 120.0, 42.0)`
- 长江流域：`(90.0, 24.0, 122.0, 35.0)`
- 珠江流域：`(102.0, 21.0, 117.0, 26.5)`

### 重采样配置

```python
# 是否启用重采样
ENABLE_RESAMPLE = True

# 新的像元大小（度）
NEW_CELLSIZE = 0.01
```

**常用分辨率**：
- `0.00833` ≈ 1km（原始分辨率）
- `0.01` ≈ 1km
- `0.05` ≈ 5km
- `0.1` ≈ 10km
- `0.25` ≈ 25km

## 使用示例

### 示例1：只裁剪不重采样

```python
# 在config.py中设置
ENABLE_CLIP = True
ENABLE_RESAMPLE = False
CLIP_BOUNDS = (110.0, 35.0, 120.0, 42.0)  # 华北地区
```

### 示例2：只重采样不裁剪

```python
# 在config.py中设置
ENABLE_CLIP = False
ENABLE_RESAMPLE = True
NEW_CELLSIZE = 0.05  # 重采样到5km
```

### 示例3：既裁剪又重采样

```python
# 在config.py中设置
ENABLE_CLIP = True
ENABLE_RESAMPLE = True
CLIP_BOUNDS = (73.0, 18.0, 135.0, 54.0)  # 中国大陆
NEW_CELLSIZE = 0.01  # 重采样到1km
```

### 示例4：不同文件夹使用不同参数

```python
# 启用自定义处理
USE_CUSTOM_PROCESSING = True

# 自定义处理配置
CUSTOM_PROCESSING = {
    'pet_2023': {
        'clip_bounds': (73.0, 18.0, 135.0, 54.0),  # 全国范围
        'new_cellsize': 0.01,
        'enable_clip': True,
        'enable_resample': True
    },
    'pet_2024': {
        'clip_bounds': (110.0, 35.0, 120.0, 42.0),  # 华北地区
        'new_cellsize': 0.05,
        'enable_clip': True,
        'enable_resample': True
    }
}
```

## 输出结果

处理完成后，文件结构如下：

```
processed_asc/
├── pet_2023/
│   ├── pet_2023_month_01.asc
│   ├── pet_2023_month_02.asc
│   └── ...
└── pet_2024/
    ├── pet_2024_month_01.asc
    ├── pet_2024_month_02.asc
    └── ...
```

## 处理过程说明

1. **读取原始ASC文件**：解析头信息和数据
2. **裁剪处理**（如果启用）：
   - 计算裁剪区域在原始数据中的索引
   - 提取对应区域的数据
   - 更新地理坐标信息
3. **重采样处理**（如果启用）：
   - 使用最近邻插值方法
   - 重新计算行列数
   - 更新像元大小
4. **写入结果文件**：保存为标准ASC格式

## 注意事项

1. **内存使用**：处理大文件时需要足够内存
2. **处理时间**：重采样到更高分辨率会增加处理时间
3. **数据精度**：重采样可能会影响数据精度
4. **坐标系统**：工具假设数据使用地理坐标系（经纬度）
5. **文件备份**：建议在处理前备份原始文件

## 错误处理

常见错误及解决方法：

1. **文件不存在**：检查输入文件夹路径
2. **内存不足**：减少处理的文件数量或降低目标分辨率
3. **裁剪范围错误**：确保裁剪范围与原始数据有重叠
4. **权限错误**：确保有写入输出文件夹的权限

## 性能优化建议

1. **分批处理**：对于大量文件，可以分批处理
2. **合理设置分辨率**：避免过度重采样
3. **使用SSD**：在SSD上处理可以提高速度
4. **关闭其他程序**：释放更多内存给处理程序

## 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.7+）
2. numpy库是否正确安装
3. 输入文件格式是否正确
4. 配置参数是否合理
