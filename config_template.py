#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASC文件处理配置模板
请根据您的需求修改以下配置参数
"""

# ===========================================
# 基本配置
# ===========================================

# 输入文件夹列表 - 包含要处理的ASC文件的文件夹
INPUT_FOLDERS = [
    'pet_2023',
    'pet_2024'
]

# 输出基础文件夹 - 处理后的文件将保存在这里
OUTPUT_BASE_FOLDER = 'processed_asc'

# ===========================================
# 裁剪配置
# ===========================================

# 是否进行裁剪
ENABLE_CLIP = True

# 裁剪范围 (xmin, ymin, xmax, ymax) - 经纬度坐标
# 常用区域范围参考：
# 中国大陆: (73.0, 18.0, 135.0, 54.0)
# 华北地区: (110.0, 35.0, 120.0, 42.0)
# 长江流域: (90.0, 24.0, 122.0, 35.0)
# 珠江流域: (102.0, 21.0, 117.0, 26.5)

CLIP_BOUNDS = (73.0, 18.0, 135.0, 54.0)  # 中国大陆范围

# ===========================================
# 重采样配置
# ===========================================

# 是否进行重采样
ENABLE_RESAMPLE = True

# 新的像元大小（度）
# 常用分辨率参考：
# 0.00833 ≈ 1km (原始分辨率)
# 0.01    ≈ 1km
# 0.05    ≈ 5km
# 0.1     ≈ 10km
# 0.25    ≈ 25km

NEW_CELLSIZE = 0.01  # 重采样到0.01度

# ===========================================
# 高级配置
# ===========================================

# 无数据值
NODATA_VALUE = -9999

# 是否显示详细处理信息
VERBOSE = True

# 是否在处理前备份原始文件
BACKUP_ORIGINAL = False

# 备份文件夹名称
BACKUP_FOLDER = 'backup_original'

# ===========================================
# 预定义区域配置
# ===========================================

# 您可以定义多个预设区域，然后在CLIP_BOUNDS中引用
PREDEFINED_REGIONS = {
    'china_mainland': (73.0, 18.0, 135.0, 54.0),
    'north_china': (110.0, 35.0, 120.0, 42.0),
    'yangtze_river': (90.0, 24.0, 122.0, 35.0),
    'pearl_river': (102.0, 21.0, 117.0, 26.5),
    'xinjiang': (73.0, 34.0, 97.0, 49.0),
    'tibet': (78.0, 26.0, 99.0, 37.0),
    'northeast': (115.0, 39.0, 135.0, 54.0),
    'southeast': (110.0, 18.0, 125.0, 32.0),
}

# 使用预定义区域的示例：
# CLIP_BOUNDS = PREDEFINED_REGIONS['china_mainland']

# ===========================================
# 批处理配置示例
# ===========================================

# 如果您需要对不同文件夹使用不同的处理参数，可以使用以下配置：
CUSTOM_PROCESSING = {
    'pet_2023': {
        'clip_bounds': (73.0, 18.0, 135.0, 54.0),
        'new_cellsize': 0.01,
        'enable_clip': True,
        'enable_resample': True
    },
    'pet_2024': {
        'clip_bounds': (110.0, 35.0, 120.0, 42.0),  # 只处理华北地区
        'new_cellsize': 0.05,  # 重采样到5km
        'enable_clip': True,
        'enable_resample': True
    }
}

# 是否使用自定义处理配置
USE_CUSTOM_PROCESSING = False

# ===========================================
# 输出文件命名配置
# ===========================================

# 输出文件名后缀
OUTPUT_SUFFIX = '_processed'

# 是否在输出文件名中包含处理信息
INCLUDE_PROCESSING_INFO = True

# 示例输出文件名：
# 原文件: pet_2023_month_01.asc
# 处理后: pet_2023_month_01_processed_clip_resample.asc

# ===========================================
# 质量控制配置
# ===========================================

# 处理前是否检查文件完整性
CHECK_FILE_INTEGRITY = True

# 处理后是否进行质量检查
QUALITY_CHECK = True

# 是否生成处理报告
GENERATE_REPORT = True

# 报告文件名
REPORT_FILENAME = 'processing_report.txt'
