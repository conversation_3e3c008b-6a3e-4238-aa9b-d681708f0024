#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASC文件批量裁剪和重采样工具
支持按指定范围裁剪和重采样ASC格式栅格文件
"""

import os
import numpy as np
from pathlib import Path
import shutil

class ASCProcessor:
    def __init__(self):
        self.nodata_value = -9999
    
    def read_asc_header(self, file_path):
        """读取ASC文件头信息"""
        header = {}
        with open(file_path, 'r') as f:
            for i in range(6):  # ASC文件前6行是头信息
                line = f.readline().strip().split()
                if len(line) >= 2:
                    key = line[0].lower()
                    value = float(line[1]) if '.' in line[1] or 'e' in line[1].lower() else int(line[1])
                    header[key] = value
        return header
    
    def read_asc_data(self, file_path):
        """读取ASC文件数据"""
        header = self.read_asc_header(file_path)
        
        # 读取数据部分
        data = np.loadtxt(file_path, skiprows=6)
        
        return header, data
    
    def write_asc_file(self, file_path, header, data):
        """写入ASC文件（优化版本）"""
        nodata = int(header.get('nodata_value', self.nodata_value))

        with open(file_path, 'w') as f:
            # 写入头信息
            f.write(f"ncols         {int(header['ncols'])}\n")
            f.write(f"nrows         {int(header['nrows'])}\n")
            f.write(f"xllcorner     {header['xllcorner']}\n")
            f.write(f"yllcorner     {header['yllcorner']}\n")
            f.write(f"cellsize      {header['cellsize']}\n")
            f.write(f"NODATA_value  {nodata}\n")

            # 优化数据写入 - 使用numpy的向量化操作
            # 处理无效值
            data_copy = data.copy()
            data_copy[np.isnan(data_copy)] = nodata
            data_copy[data_copy == self.nodata_value] = nodata

            # 转换为整数并写入
            for row in data_copy:
                # 使用numpy的向量化操作转换为整数
                int_row = row.astype(int)
                row_str = ' '.join(map(str, int_row))
                f.write(row_str + '\n')
    
    def clip_asc(self, input_file, output_file, clip_bounds):
        """
        裁剪ASC文件
        
        Parameters:
        - input_file: 输入ASC文件路径
        - output_file: 输出ASC文件路径
        - clip_bounds: 裁剪范围 (xmin, ymin, xmax, ymax)
        """
        print(f"正在裁剪: {input_file}")
        
        # 读取原始数据
        header, data = self.read_asc_data(input_file)
        
        xmin, ymin, xmax, ymax = clip_bounds
        
        # 计算原始数据的地理范围
        orig_xmin = header['xllcorner']
        orig_ymin = header['yllcorner']
        orig_xmax = orig_xmin + header['ncols'] * header['cellsize']
        orig_ymax = orig_ymin + header['nrows'] * header['cellsize']
        
        print(f"  原始范围: X({orig_xmin:.6f}, {orig_xmax:.6f}), Y({orig_ymin:.6f}, {orig_ymax:.6f})")
        print(f"  裁剪范围: X({xmin:.6f}, {xmax:.6f}), Y({ymin:.6f}, {ymax:.6f})")
        
        # 检查裁剪范围是否与原始数据重叠
        if xmax <= orig_xmin or xmin >= orig_xmax or ymax <= orig_ymin or ymin >= orig_ymax:
            print(f"  警告: 裁剪范围与原始数据不重叠，跳过文件")
            return False
        
        # 调整裁剪范围到数据范围内
        clip_xmin = max(xmin, orig_xmin)
        clip_ymin = max(ymin, orig_ymin)
        clip_xmax = min(xmax, orig_xmax)
        clip_ymax = min(ymax, orig_ymax)
        
        # 计算裁剪区域在原始数据中的行列索引
        col_start = int((clip_xmin - orig_xmin) / header['cellsize'])
        col_end = int((clip_xmax - orig_xmin) / header['cellsize'])
        
        # 注意：ASC文件中行是从上到下的，所以需要转换
        row_start = int((orig_ymax - clip_ymax) / header['cellsize'])
        row_end = int((orig_ymax - clip_ymin) / header['cellsize'])
        
        # 确保索引在有效范围内
        col_start = max(0, min(col_start, header['ncols'] - 1))
        col_end = max(col_start + 1, min(col_end, header['ncols']))
        row_start = max(0, min(row_start, header['nrows'] - 1))
        row_end = max(row_start + 1, min(row_end, header['nrows']))
        
        print(f"  裁剪索引: 行({row_start}-{row_end}), 列({col_start}-{col_end})")
        
        # 裁剪数据
        clipped_data = data[row_start:row_end, col_start:col_end]
        
        # 更新头信息
        new_header = header.copy()
        new_header['ncols'] = clipped_data.shape[1]
        new_header['nrows'] = clipped_data.shape[0]
        new_header['xllcorner'] = orig_xmin + col_start * header['cellsize']
        new_header['yllcorner'] = orig_ymax - row_end * header['cellsize']
        
        print(f"  裁剪后大小: {new_header['nrows']} × {new_header['ncols']}")
        print(f"  裁剪后范围: X({new_header['xllcorner']:.6f}, {new_header['xllcorner'] + new_header['ncols'] * new_header['cellsize']:.6f})")
        print(f"              Y({new_header['yllcorner']:.6f}, {new_header['yllcorner'] + new_header['nrows'] * new_header['cellsize']:.6f})")
        
        # 写入裁剪后的文件
        self.write_asc_file(output_file, new_header, clipped_data)
        print(f"  ✓ 裁剪完成: {output_file}")
        
        return True
    
    def resample_asc(self, input_file, output_file, new_cellsize):
        """
        重采样ASC文件
        
        Parameters:
        - input_file: 输入ASC文件路径
        - output_file: 输出ASC文件路径
        - new_cellsize: 新的像元大小
        """
        print(f"正在重采样: {input_file}")
        
        # 读取原始数据
        header, data = self.read_asc_data(input_file)
        
        old_cellsize = header['cellsize']
        print(f"  原始像元大小: {old_cellsize}")
        print(f"  目标像元大小: {new_cellsize}")
        
        if abs(old_cellsize - new_cellsize) < 1e-10:
            print(f"  像元大小相同，直接复制文件")
            shutil.copy2(input_file, output_file)
            return True
        
        # 计算重采样比例
        scale_factor = old_cellsize / new_cellsize
        print(f"  重采样比例: {scale_factor:.6f}")
        
        # 计算新的行列数
        new_ncols = int(header['ncols'] * scale_factor)
        new_nrows = int(header['nrows'] * scale_factor)
        
        print(f"  原始大小: {header['nrows']} × {header['ncols']}")
        print(f"  重采样后大小: {new_nrows} × {new_ncols}")
        
        # 创建新的数据数组
        new_data = np.full((new_nrows, new_ncols), header.get('nodata_value', self.nodata_value))
        
        # 进行重采样（最近邻插值）
        for new_row in range(new_nrows):
            for new_col in range(new_ncols):
                # 计算在原始数据中对应的位置
                old_row = int(new_row / scale_factor)
                old_col = int(new_col / scale_factor)
                
                # 确保索引在有效范围内
                if 0 <= old_row < header['nrows'] and 0 <= old_col < header['ncols']:
                    new_data[new_row, new_col] = data[old_row, old_col]
        
        # 更新头信息
        new_header = header.copy()
        new_header['ncols'] = new_ncols
        new_header['nrows'] = new_nrows
        new_header['cellsize'] = new_cellsize
        
        # 写入重采样后的文件
        self.write_asc_file(output_file, new_header, new_data)
        print(f"  ✓ 重采样完成: {output_file}")
        
        return True
    
    def process_folder(self, input_folder, output_folder, clip_bounds=None, new_cellsize=None):
        """
        批量处理文件夹中的ASC文件
        
        Parameters:
        - input_folder: 输入文件夹路径
        - output_folder: 输出文件夹路径
        - clip_bounds: 裁剪范围 (xmin, ymin, xmax, ymax)，None表示不裁剪
        - new_cellsize: 新的像元大小，None表示不重采样
        """
        input_path = Path(input_folder)
        output_path = Path(output_folder)
        
        if not input_path.exists():
            print(f"错误: 输入文件夹不存在 {input_folder}")
            return
        
        # 创建输出文件夹
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找所有ASC文件
        asc_files = list(input_path.glob('*.asc'))
        
        if not asc_files:
            print(f"在文件夹 {input_folder} 中未找到ASC文件")
            return
        
        print(f"找到 {len(asc_files)} 个ASC文件")
        print(f"输出文件夹: {output_folder}")
        
        if clip_bounds:
            print(f"裁剪范围: X({clip_bounds[0]}, {clip_bounds[2]}), Y({clip_bounds[1]}, {clip_bounds[3]})")
        
        if new_cellsize:
            print(f"重采样像元大小: {new_cellsize}")
        
        print("-" * 60)
        
        success_count = 0
        
        for asc_file in asc_files:
            try:
                temp_file = None
                final_output = output_path / asc_file.name
                
                # 如果需要裁剪和重采样，先处理到临时文件
                if clip_bounds and new_cellsize:
                    temp_file = output_path / f"temp_{asc_file.name}"
                    
                    # 先裁剪
                    if self.clip_asc(asc_file, temp_file, clip_bounds):
                        # 再重采样
                        if self.resample_asc(temp_file, final_output, new_cellsize):
                            success_count += 1
                    
                    # 删除临时文件
                    if temp_file and temp_file.exists():
                        temp_file.unlink()
                
                elif clip_bounds:
                    # 只裁剪
                    if self.clip_asc(asc_file, final_output, clip_bounds):
                        success_count += 1
                
                elif new_cellsize:
                    # 只重采样
                    if self.resample_asc(asc_file, final_output, new_cellsize):
                        success_count += 1
                
                else:
                    # 既不裁剪也不重采样，直接复制
                    shutil.copy2(asc_file, final_output)
                    print(f"复制文件: {asc_file} -> {final_output}")
                    success_count += 1
                
            except Exception as e:
                print(f"处理文件 {asc_file} 时出错: {e}")
        
        print("-" * 60)
        print(f"处理完成! 成功处理 {success_count}/{len(asc_files)} 个文件")

def load_config():
    """加载配置文件"""
    try:
        import config_template as config
        return config
    except ImportError:
        print("未找到配置文件，使用默认配置")
        return None

def get_processing_params(config, folder_name):
    """获取处理参数"""
    if config and hasattr(config, 'USE_CUSTOM_PROCESSING') and config.USE_CUSTOM_PROCESSING:
        if folder_name in config.CUSTOM_PROCESSING:
            custom = config.CUSTOM_PROCESSING[folder_name]
            return {
                'clip_bounds': custom.get('clip_bounds') if custom.get('enable_clip', True) else None,
                'new_cellsize': custom.get('new_cellsize') if custom.get('enable_resample', True) else None
            }

    # 使用全局配置
    if config:
        return {
            'clip_bounds': getattr(config, 'CLIP_BOUNDS', None) if getattr(config, 'ENABLE_CLIP', True) else None,
            'new_cellsize': getattr(config, 'NEW_CELLSIZE', None) if getattr(config, 'ENABLE_RESAMPLE', True) else None
        }

    # 默认配置
    return {
        'clip_bounds': (73.0, 18.0, 135.0, 54.0),  # 中国大陆范围
        'new_cellsize': 0.01  # 重采样到0.01度
    }

def main():
    """主函数"""
    processor = ASCProcessor()

    print("ASC文件批量裁剪和重采样工具")
    print("=" * 60)

    # 加载配置
    config = load_config()

    # 获取输入文件夹列表
    if config and hasattr(config, 'INPUT_FOLDERS'):
        input_folders = config.INPUT_FOLDERS
    else:
        input_folders = ['pet_2023', 'pet_2024']

    # 获取输出基础文件夹
    if config and hasattr(config, 'OUTPUT_BASE_FOLDER'):
        output_base = config.OUTPUT_BASE_FOLDER
    else:
        output_base = 'processed_asc'

    print(f"输入文件夹: {input_folders}")
    print(f"输出基础文件夹: {output_base}")

    # 处理所有文件夹
    for input_folder in input_folders:
        if not Path(input_folder).exists():
            print(f"跳过不存在的文件夹: {input_folder}")
            continue

        # 获取处理参数
        params = get_processing_params(config, input_folder)

        output_folder = Path(output_base) / input_folder

        print(f"\n处理文件夹: {input_folder}")
        print(f"输出到: {output_folder}")
        print(f"裁剪范围: {params['clip_bounds']}")
        print(f"重采样大小: {params['new_cellsize']}")

        processor.process_folder(
            input_folder=input_folder,
            output_folder=output_folder,
            clip_bounds=params['clip_bounds'],
            new_cellsize=params['new_cellsize']
        )

if __name__ == "__main__":
    main()
